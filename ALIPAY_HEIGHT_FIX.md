# 支付宝小程序高度递增问题解决方案

## 问题描述

在支付宝小程序中，z-paging 的 `contentHeightChanged` 事件传入的高度每次刷新都在递增（如 319.1875px），导致页面布局异常。

## 问题根本原因

1. **支付宝小程序特殊处理**：在 z-paging 源码的 `common-layout.js` 中，支付宝小程序强制将 `inDom` 设为 `false`
2. **测量范围扩大**：导致 `uni.createSelectorQuery()` 不使用 `.in(this)` 限定范围，测量的是整个页面而不是组件内部
3. **循环累加**：每次测量都包含了页面其他部分的高度，形成循环累加

## 解决方案：固定高度 + 内部滚动

### 核心思路
- **固定 swiper 高度**：通过 JavaScript 计算屏幕可用高度，设置固定的 swiper 高度
- **z-paging 内部滚动**：移除 `use-page-scroll`，让 z-paging 组件内部滚动
- **避免高度测量**：完全不依赖 `contentHeightChanged` 事件

### 实现步骤

#### 1. 修改首页 (src/pages/index/index.vue)

```javascript
// 计算并设置固定的swiper高度
function calculateSwiperHeight() {
  const systemInfo = uni.getSystemInfoSync()
  
  // 计算各部分高度
  const bannerHeight = uni.upx2px(370 + 80 + 40) // 搜索框 + 图片区域 + 间距
  const tabHeight = uni.upx2px(80) // tab栏高度
  
  // 计算可用高度：屏幕高度 - 搜索框和图片区域 - tab栏
  const availableHeight = systemInfo.windowHeight - bannerHeight - tabHeight
  const finalHeight = Math.max(availableHeight, 400) // 最小高度400px
  
  swiperHeight.value = finalHeight
}

// 页面加载时计算高度
onLoad(() => {
  calculateSwiperHeight()
})
```

#### 2. 修改列表组件 (src/components/PhotoSizeList.vue)

```html
<!-- 移除 use-page-scroll 和 @content-height-changed -->
<z-paging
  v-show="!isSkeletonLoading"
  ref="paging"
  v-model="dataList"
  :hide-empty-view="hideEmptyView"
  :refresher-enabled="false"
  :auto="false"
  :auto-clean-list-when-reload="false"
  :default-page-size="10"
  @query="queryList"
>
```

```css
/* 确保组件可以内部滚动 */
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
```

## 方案优势

1. **完全避免高度测量问题**：不再依赖支付宝小程序有问题的高度测量机制
2. **更好的性能**：避免频繁的高度计算和重新渲染
3. **更稳定的用户体验**：滚动行为在所有平台上都保持一致
4. **代码更简洁**：移除了复杂的高度检测和防抖逻辑

## 注意事项

1. 需要根据实际的页面布局调整高度计算公式
2. 确保 swiper 的父容器有固定高度
3. 在不同设备上测试高度计算的准确性

## 测试建议

1. 在支付宝小程序中测试多次刷新，确认高度不再递增
2. 测试不同屏幕尺寸的设备
3. 测试 tab 切换和列表滚动的流畅性
