<!-- 在这个文件对每个tab对应的列表进行渲染 -->
<script lang="ts" setup>
import type { IPhotoItem } from '@/api/photo'
import { getPhotoList } from '@/api/photo'

interface Props {
  // 当前组件的index，也就是当前组件是swiper中的第几个
  tabIndex?: number
  // 当前swiper切换到第几个index
  currentIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  tabIndex: 0,
  currentIndex: 0,
})

const emit = defineEmits<{
  heightChanged: [height: number]
}>()

// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
const dataList = ref<IPhotoItem[]>([])
const hideEmptyView = ref(true)
const completeFunc = ref<(() => void) | null>(null)

// 骨架屏加载状态
const isSkeletonLoading = ref(false)

// z-paging实例引用
const paging = ref()

// 骨架屏配置 - 模拟列表项结构
const skeletonConfig = [
  { width: '100%', height: '0rpx' }, // 空白占位，模拟上边距
  // 第一个列表项
  [
    { width: '60%', height: '32rpx' }, // 标题
    { width: '80rpx', height: '56rpx', marginLeft: 'auto' }, // 详情按钮
  ],
  { width: '100%', height: '1px' }, // 分割线
  // 第二个列表项
  [
    { width: '70%', height: '32rpx' },
    { width: '80rpx', height: '56rpx', marginLeft: 'auto' },
  ],
  { width: '100%', height: '1px' },
  // 第三个列表项
  [
    { width: '55%', height: '32rpx' },
    { width: '80rpx', height: '56rpx', marginLeft: 'auto' },
  ],
  { width: '100%', height: '1px' },
  // 第四个列表项
  [
    { width: '65%', height: '32rpx' },
    { width: '80rpx', height: '56rpx', marginLeft: 'auto' },
  ],
  { width: '100%', height: '1px' },
  // 第五个列表项
  [
    { width: '50%', height: '32rpx' },
    { width: '80rpx', height: '56rpx', marginLeft: 'auto' },
  ],
]

// 监听当前index变化 - 按照官方示例的写法
watch(() => props.currentIndex, (newVal) => {
  if (newVal === props.tabIndex) {
    setTimeout(() => {
      if (paging.value) {
        isSkeletonLoading.value = true
        paging.value.reload()
      }
    }, 100)
  }
}, {
  immediate: true,
})

// 查询列表数据
async function queryList(pageNo: number, pageSize: number) {
  // 如果是第一页，显示骨架屏
  if (pageNo === 1) {
    isSkeletonLoading.value = true
  }

  try {
    // 调用真实API接口
    const response = await getPhotoList({
      pageNum: pageNo,
      pageSize,
      type: props.tabIndex + 1, // tabIndex从0开始，type从1开始
    })

    paging.value?.completeByTotal(response.records, response.total)
    hideEmptyView.value = false

    // 数据加载完成，隐藏骨架屏
    isSkeletonLoading.value = false
    // 请求结束，调用父组件的下拉刷新结束回调函数，使得父组件中的z-paging下拉刷新结束
    if (completeFunc.value) {
      completeFunc.value()
    }
  }
  catch (error) {
    console.error('获取证件照列表失败:', error)
    uni.showToast({
      title: '获取数据失败，请重试',
      icon: 'none',
    })
    // 按照z-paging官方文档，请求失败时调用complete(false)
    paging.value?.complete(false)
    // 隐藏骨架屏
    isSkeletonLoading.value = false
    // 请求结束，调用父组件的下拉刷新结束回调函数，使得父组件中的z-paging下拉刷新结束
    if (completeFunc.value) {
      completeFunc.value()
    }
  }
}

// 页面通知当前子组件刷新列表
function reload(completeCallback?: () => void) {
  // 显示骨架屏
  isSkeletonLoading.value = true
  // 先把父组件下拉刷新的回调函数存起来
  completeFunc.value = completeCallback || null
  // 调用z-paging的reload方法
  paging.value?.reload()
}

// 点击列表项
function goToDetail(_item: IPhotoItem) {
  // TODO: 实现列表项点击逻辑
}

// 当列表高度改变时，通知页面的swiper同步更改高度
function contentHeightChanged(height: number) {
  const finalHeight = dataList.value.length ? height : 0
  // 限制内容最小高度为屏幕可见高度减Tab选项卡高度
  const minHeight = uni.getSystemInfoSync().windowHeight - uni.upx2px(80)
  console.log('传入的高度:', height)
  emit('heightChanged', Math.max(finalHeight, minHeight))
}

// 页面通知当前子组件加载更多数据
function doLoadMore() {
  paging.value?.doLoadMore()
}

// 页面通知当前子组件清除数据
function clear() {
  paging.value?.clear()
  hideEmptyView.value = true
}

// 暴露方法给父组件调用
defineExpose({
  reload,
  doLoadMore,
  clear,
})
</script>

<template>
  <view class="content">
    <!-- 骨架屏 -->
    <wd-skeleton
      :loading="isSkeletonLoading"
      :row-col="skeletonConfig"
      animation="gradient"
      :custom-style="{ marginLeft: '35rpx', marginRight: '35rpx' }"
    />

    <!-- z-paging列表 -->
    <z-paging
      v-show="!isSkeletonLoading"
      ref="paging"
      v-model="dataList"
      use-page-scroll
      :scrollable="false"
      :hide-empty-view="hideEmptyView"
      :refresher-enabled="false"
      :auto="false"
      :auto-clean-list-when-reload="false"
      :default-page-size="10"
      @query="queryList"
      @content-height-changed="contentHeightChanged"
    >
      <!-- 自定义空状态页面 -->
      <template #empty>
        <view class="custom-empty">
          <view class="empty-icon">
            📷
          </view>
          <view class="empty-text">
            暂无{{ ['常用尺寸', '各类签证', '各类证件'][tabIndex] || '常用尺寸' }}数据
          </view>
          <view class="empty-tip">
            请刷新再试
          </view>
        </view>
      </template>

      <!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
      <view v-for="(item, index) in dataList" :key="index" class="item-container">
        <view class="item-card" @click="goToDetail(item)">
          <view class="item-content">
            <view class="item-title">
              {{ item.name }}
            </view>
            <view class="item-info">
              <view class="info-row">
                <view class="tag">
                  宽高
                </view>
                <text class="info-text">
                  {{ item.widthMm }}*{{ item.heightMm }}mm
                </text>
                <view class="tag tag-margin">
                  像素
                </view>
                <text class="info-text">
                  {{ item.widthPx }}*{{ item.heightPx }}px
                </text>
              </view>
            </view>
          </view>
          <view class="item-icon">
            <image class="camera-icon" src="/static/icon/camera.png" mode="aspectFit" />
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
/* 注意，1、父节点需要固定高度，z-paging的height:100%才会生效 */
/* 注意，2、请确保z-paging与同级的其他view的总高度不得超过屏幕宽度，以避免超出屏幕高度时页面的滚动与z-paging内部的滚动冲突 */
.content {
  height: 100%;
}

.item-container {
  margin: 20rpx 0 0rpx 0;
}

.item-card {
  width: 100%;
  height: 132rpx;
  background: #ffffff;
  box-shadow: 2rpx 1rpx 4rpx 3rpx rgba(52, 51, 51, 0.06);
  border-radius: 20rpx;
  display: flex;
  flex-direction: row;
}

.item-content {
  flex: 1;
  margin-left: 35rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.item-title {
  font-weight: 600;
  font-size: 28rpx;
  line-height: 39rpx;
  color: #323232;
  margin-bottom: 10rpx;
}

.item-info {
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.tag {
  height: 30rpx;
  width: 60rpx;
  background: #e8e8ff;
  border-radius: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bdbdff;
  font-size: 20rpx;
  line-height: 28rpx;
}

.tag-margin {
  margin-left: 66rpx;
}

.info-text {
  font-size: 24rpx;
  line-height: 34rpx;
  color: #d3d3d3;
  margin-left: 16rpx;
}

.item-icon {
  width: 80rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.camera-icon {
  width: 44rpx;
  height: 44rpx;
}

/* 自定义空状态样式 */
.custom-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  min-height: 400rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
}
</style>
